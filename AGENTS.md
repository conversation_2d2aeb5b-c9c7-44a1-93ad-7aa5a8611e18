 # MTI.hu News JSON Feed API (mti-feed)

 ## Overview
 This application automatically collects news articles from the MTI.hu website and serves them in JSON Feed format. It runs in a Docker container and emphasizes resource-efficient, human-like scraping with session management and rate limiting.

 ## Key Features
 - Secure MTI.hu login and session management
 - Multi-category news scraping with randomized delays
 - Standards-compliant JSON Feed 1.1 output
 - Embedded content extraction (YouTube, Facebook, Twitter, Instagram)
 - Health check (`/health`) and metrics (`/metrics`) endpoints
 - Category-based in-memory caching with size limits
 - Resource-efficient single-threaded sequential processing

 ## Technology Stack
 - Node.js
 - Fastify web framework
 - Playwright for web scraping automation
 - node-cron for scheduling
 - <PERSON><PERSON> for logging
 - Vitest for testing
 - Docker (Alpine Linux base) and Docker Compose

 ## Architecture
 The project follows a clean separation of concerns:
 ```
 app.js                # Main Fastify server entry point
 controllers/          # Request handlers (feed, health, metrics)
 routes/               # API route definitions
 services/             # Business logic (authentication, scraping, caching)
 utils/                # Utility modules (config, env, errors, helpers, logger, embeddedContent)
 ```

 ## Project Structure
 ```
 mti-feed/
 ├── config/
 ├── data/
 ├── scripts/
 ├── src/
 ├── tests/
 ├── .env (.gitignored)
 ├── .gitignore
 ├── docker-compose.yml
 ├── Dockerfile
 ├── package.json
 ├── README.md
 ├── TESTING.md
 └── vitest.config.js
 ```

 ## Setup and Installation
 1. Clone the repository and install dependencies:
    ```bash
    git clone <repository-url>
    cd mti-feed
    npm install --include=dev
    ```
 2. Copy `.env.sample` to `.env` and fill in your MTI.hu credentials.
 3. Start the application:
    ```bash
    npm start
    ```

 ## Environment Variables
 - `MTI_EMAIL` and `MTI_PASSWORD` for MTI.hu authentication
 - `NODE_ENV`, `PORT`, `LOG_LEVEL`, etc. (see `.env.sample`)

 ## Docker Setup
 To run in Docker:
 ```bash
 docker-compose up --build -d
 ```
 The API will be available at `http://localhost:8080/feed`.

 ## API Endpoints
 - `GET /feed` — All news items in JSON Feed format
 - `GET /feed/:category` — News by category (`kozelet`, `gazdasag`, `vilag`, `kultura`, `sport`, `english`)
 - `GET /health` — Service health status
 - `GET /metrics` — System and cache metrics

 ## Configuration
 Configuration is managed via `config/default.json` and environment variables (based on dotenv). Key settings include scraping intervals, timeouts, caching, and logging levels.

 ## Testing
 - Vitest framework with unit, integration, and basic test suites
 - 127 tests across 11 test files
 - ~78.75% overall code coverage
 - Test commands:
   ```bash
   npm run test:run
   npm run test:coverage
   npm run test:ui
   ```

 ## License
 MIT License